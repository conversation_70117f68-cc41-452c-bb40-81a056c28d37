'use client'

import { Terminal as XTerm } from '@xterm/xterm'
import { FitAddon } from '@xterm/addon-fit'
// import { WebLinksAddon } from "@xterm/addon-web-links";
// import { SearchAddon } from "@xterm/addon-search";
import { useEffect, useRef } from 'react'
import '@xterm/xterm/css/xterm.css'
import clsx from 'clsx'
import { useTheme } from 'next-themes'
import { useTerminal } from '@/contexts/terminal-context'
import { ActionStep } from '@/typings/agent'

interface TerminalProps {
    className?: string
    currentActionData?: ActionStep
}

const Terminal = ({ className, currentActionData }: TerminalProps) => {
    const { theme } = useTheme()
    const { xtermRef } = useTerminal()
    const terminalRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const interval = setInterval(() => {
            const container = terminalRef.current
            if (
                container &&
                container.clientWidth > 0 &&
                container.clientHeight > 0 &&
                !(xtermRef && 'current' in xtermRef ? xtermRef.current : null)
            ) {
                clearInterval(interval)

                const term = new XTerm({
                    cursorBlink: true,
                    fontSize: 14,
                    fontFamily: 'monospace',
                    theme: {
                        background:
                            theme === 'dark'
                                ? 'rgba(33, 33, 33, 1)'
                                : 'rgba(255, 255, 255, 1)',
                        foreground: theme === 'dark' ? '#ffffff' : '#212121',
                        cursor: '#ffffff',
                        cursorAccent: '#1a1b26',
                        selectionBackground: 'rgba(255, 255, 255, 0.3)',
                        selectionForeground: undefined
                    },
                    allowTransparency: true
                })

                const fitAddon = new FitAddon()
                term.loadAddon(fitAddon)
                // term.loadAddon(new WebLinksAddon());
                // term.loadAddon(new SearchAddon());

                term.open(container)
                fitAddon.fit()
                prompt(term)

                const handleResize = () => {
                    fitAddon.fit()
                }
                window.addEventListener('resize', handleResize)

                if (xtermRef) {
                    xtermRef.current = term
                }

                return () => {
                    window.removeEventListener('resize', handleResize)
                    term.dispose()
                }
            }
        }, 100)

        return () => clearInterval(interval)
    }, [theme, xtermRef])

    // Handle theme changes for existing terminal
    useEffect(() => {
        if (xtermRef?.current) {
            xtermRef.current.options.theme = {
                background:
                    theme === 'dark'
                        ? 'rgba(0,0,0,0.8)'
                        : 'rgba(255, 255, 255, 1)',
                foreground: theme === 'dark' ? '#ffffff' : '#212121',
                cursor: '#ffffff',
                cursorAccent: '#1a1b26',
                selectionBackground: 'rgba(255, 255, 255, 0.3)',
                selectionForeground: undefined
            }
        }
    }, [theme, xtermRef])

    // Handle bash output from currentActionData
    useEffect(() => {
        if (!xtermRef?.current || !currentActionData) return

        // Handle bash tool outputs
        const result = currentActionData.data?.result
        if (result) {
            // Clear terminal for new bash command
            xtermRef.current?.reset()

            // Write the output
            const lines = `${result}`.split('\n')
            lines.forEach((line, index) => {
                if (index === lines.length - 1 && line.trim()) {
                    // Last line without newline
                    xtermRef.current.write(line)
                } else {
                    // Regular lines with newline
                    xtermRef.current.writeln(line)
                }
            })

            // Scroll to bottom
            xtermRef.current.scrollToBottom()
        }
    }, [currentActionData, xtermRef])

    const prompt = (term: XTerm) => {
        scrollToBottom(term)
    }

    const scrollToBottom = (term: XTerm) => {
        term.scrollToBottom()
    }

    return (
        <div className={clsx('p-4 h-full overflow-auto', className)}>
            <div ref={terminalRef} className="h-full w-full" />
        </div>
    )
}

export default Terminal
