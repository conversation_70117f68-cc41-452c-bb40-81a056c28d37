import os
import j<PERSON>

from typing import Dict
from mcp.types import ToolAnnotations
from fastmcp import FastMCP
from fastmcp.server.proxy import ProxyClient
from argparse import ArgumentParser
from ii_tool.tools.manager import get_sandbox_tools
from ii_tool.mcp_integrations.manager import get_mcp_integrations
from dotenv import load_dotenv

load_dotenv()


async def create_mcp(workspace_dir: str, tool_server_url: str, custom_mcp_config: Dict = None):    
    tools = get_sandbox_tools(
        workspace_path=workspace_dir,
        tool_server_url=tool_server_url,
    )

    main_server = FastMCP()

    for tool in tools:
        main_server.tool(
            tool.execute_mcp_wrapper,
            name=tool.name,
            description=tool.description,
            annotations=ToolAnnotations(
                title=tool.display_name,
                readOnlyHint=tool.read_only,
            ),
        )

        # NOTE: this is a temporary fix to set the parameters of the tool
        _mcp_tool = await main_server._tool_manager.get_tool(tool.name)
        _mcp_tool.parameters = tool.input_schema


    # Our system defined MCP integrations
    mcp_integrations = get_mcp_integrations(workspace_dir)
    for mcp_integration in mcp_integrations:
        proxy = FastMCP.as_proxy(ProxyClient(mcp_integration.config))
        for tool_name in mcp_integration.selected_tool_names:
            mirrored_tool = await proxy.get_tool(tool_name)
            local_tool = mirrored_tool.copy()
            main_server.add_tool(local_tool)

    # User customized MCP integrations
    if custom_mcp_config:
        print(custom_mcp_config)
        proxy = FastMCP.as_proxy(ProxyClient(custom_mcp_config))
        main_server.mount(proxy, prefix="mcp")

    return main_server

async def main():
    parser = ArgumentParser()
    parser.add_argument("--workspace_dir", type=str, default=None)
    parser.add_argument("--tool_server_url", type=str, default=None)
    parser.add_argument("--custom_mcp_config", type=str, default=None)
    parser.add_argument("--port", type=int, default=6060)
    
    args = parser.parse_args()

    workspace_dir = os.getenv("WORKSPACE_DIR")
    if args.workspace_dir:
        workspace_dir = args.workspace_dir

    if not workspace_dir:
        raise ValueError("workspace_dir is not set. Please set the WORKSPACE_DIR environment variable or pass it as an argument --workspace_dir")

    os.makedirs(workspace_dir, exist_ok=True)

    tool_server_url = args.tool_server_url
    if not tool_server_url:
        tool_server_url = os.getenv("TOOL_SERVER_URL")
        if not tool_server_url:
            raise ValueError("tool_server_url is not set. Please set the TOOL_SERVER_URL environment variable or pass it as an argument --tool_server_url")

    custom_mcp_config = args.custom_mcp_config
    if custom_mcp_config:
        with open(custom_mcp_config, "r") as f:
            custom_mcp_config = json.load(f)

    mcp = await create_mcp(workspace_dir=workspace_dir, tool_server_url=tool_server_url, custom_mcp_config=custom_mcp_config)
    await mcp.run_async(transport="http", host="0.0.0.0", port=args.port)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())