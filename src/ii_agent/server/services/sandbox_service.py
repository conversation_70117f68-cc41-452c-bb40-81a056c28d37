"""Sandbox manager for handling sandbox lifecycle and operations."""

import uuid
import asyncio
import logging
from datetime import datetime, timezone
from typing import IO, AsyncIterator, Callable, Literal, Optional, List, Dict, Any

from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.db.manager import Sandboxes
from ii_agent.db.models import Sandbox
from ii_tool.sandbox.providers.base import Sandbox
from ii_tool.sandbox.sandbox_factory import SandboxFactory
from ii_tool.sandbox.sandbox_queue import SandboxQueueScheduler
from ii_tool.sandbox.models import (
    SandboxInfo,
)

logger = logging.getLogger(__name__)

# Timeout configuration constants
PAUSE_BEFORE_TIMEOUT_SECONDS = 600  # 10 minutes - pause sandbox this many seconds before timeout
TIMEOUT_BUFFER_SECONDS = 300  # 5 minutes - buffer after scheduled pause to prevent premature termination
DEFAULT_TIMEOUT_EXTENSION_MINUTES = 30  # Default extension time when extending timeout

class SandboxService:
    """Manager class for handling sandbox operations."""

    def __init__(
        self, 
        config: IIAgentConfig,
    ):
        self.sandbox_config = config.sandbox_config
        self.sandbox_provider = SandboxFactory.get_provider(self.sandbox_config.provider_type)
        self.timeout_seconds = self.sandbox_config.timeout_seconds
        
        # Initialize message queue if configured
        self.queue_provider = SandboxQueueScheduler(
            redis_url=self.sandbox_config.redis_url,
            redis_tls_ca_path=self.sandbox_config.redis_tls_ca_path,
            queue_name=self.sandbox_config.queue_name,
            max_retries=self.sandbox_config.max_retries
        )
        # Queue consumer will be initialized when first needed
        self._consumer_task = None
        self._consumer_lock = asyncio.Lock()
    

    async def create_sandbox(
        self,
        sandbox_id: str,
        user_id: str,
    ) -> Sandbox:
        """Create a new sandbox for a user.

        Args:
            user_id: User ID who owns the sandbox

        Returns:
            Created sandbox information
        """
        await self._ensure_consumer_started()
        try: 
            sandbox = await self.sandbox_provider.create(
                config=self.sandbox_config,
                queue=self.queue_provider,
                sandbox_id=sandbox_id,
            )

        except Exception as e:
            raise Exception(f"Failed to create sandbox {sandbox_id}: {e}") from e
        
        # Create database record
        await Sandboxes.create_sandbox(
            sandbox_id=sandbox_id,
            provider=self.sandbox_config.provider_type,
            provider_sandbox_id=sandbox.provider_sandbox_id,
            user_id=user_id,
            status="running",
        )
        return sandbox
    
    async def schedule_timeout(self, sandbox_id: str, timeout_seconds: int):
        """Schedule a timeout for the sandbox."""
        await self._ensure_consumer_started()
        sandbox_data = await Sandboxes.get_sandbox_by_id(sandbox_id)
        if not sandbox_data:
            raise Exception(f"Sandbox {sandbox_id} not found")
        try:
            await self.sandbox_provider.schedule_timeout(
                provider_sandbox_id=str(sandbox_data.provider_sandbox_id),
                sandbox_id=sandbox_id,
                config=self.sandbox_config,
                queue=self.queue_provider,
                timeout_seconds=timeout_seconds,
            )
        except Exception as e:
            raise Exception(f"Failed to schedule timeout for sandbox {sandbox_id}: {e}") from e

    async def connect_or_resume_sandbox(self, sandbox_id: str) -> Sandbox:
        """Connect to a sandbox or resume it if it's paused.

        Args:
            sandbox_id: Internal sandbox ID

        Returns:
            SandboxBaseProvider if started successfully
        """
        await self._ensure_consumer_started()
        sandbox_data = await Sandboxes.get_sandbox_by_id(sandbox_id)
        if not sandbox_data:
            raise Exception(f"Sandbox {sandbox_id} not found")
        
        if str(sandbox_data.status) == "paused":
            return await self._resume_sandbox(sandbox_id)
        elif str(sandbox_data.status) == "running":
            return await self._connect_sandbox(sandbox_id)
        else:
            raise Exception(f"Sandbox {sandbox_id} is not paused or running")
    
    async def _resume_sandbox(self, sandbox_id: str) -> Sandbox:
        """Resume a sandbox.

        Args:
            sandbox_id: Internal sandbox ID

        Returns:
            SandboxBaseProvider if resumed successfully
        """
        await self._ensure_consumer_started()
        sandbox = await Sandboxes.get_sandbox_by_id(sandbox_id)
        if not sandbox:
            raise Exception(f"Sandbox {sandbox_id} not found")
        
        if str(sandbox.status) != "paused":
            raise Exception(f"Sandbox {sandbox_id} is not paused. Cannot resume.")

        try:
            sandbox = await self.sandbox_provider.resume(
                provider_sandbox_id=str(sandbox.provider_sandbox_id),
                config=self.sandbox_config,
                queue=self.queue_provider,
                sandbox_id=sandbox_id,
            )
        except Exception as e:
            logger.error(f"Failed to resume sandbox {sandbox_id}: {e}")
            raise Exception(f"Failed to resume sandbox {sandbox_id}: {e}") from e


        logger.info(f"Resumed sandbox {sandbox_id} successfully")
        await Sandboxes.update_sandbox_status(
            sandbox_id,
            "running",
            last_activity_at=True
        )

        return sandbox

    async def _connect_sandbox(self, sandbox_id: str) -> Sandbox:
        """Connect to a sandbox.

        Args:
            sandbox_id: Internal sandbox ID

        Returns:
            SandboxBaseProvider if started successfully
        """
        await self._ensure_consumer_started()
        sandbox_data = await Sandboxes.get_sandbox_by_id(sandbox_id)
        if not sandbox_data or str(sandbox_data.status) != "running":
            raise Exception(f"Sandbox {sandbox_id} not found or not running")
        
        try:
            sandbox = await self.sandbox_provider.connect(
                provider_sandbox_id=str(sandbox_data.provider_sandbox_id),
                config=self.sandbox_config,
                queue=self.queue_provider,
                sandbox_id=sandbox_id,
            )
        except Exception as e:
            logger.error(f"Failed to connect to sandbox {sandbox_id}: {e}")
            raise Exception(f"Failed to connect to sandbox {sandbox_id}: {e}") from e

        await Sandboxes.update_sandbox_status(
            sandbox_id,
            "running",
            started_at=True,
            last_activity_at=True
        )
        logger.info(f"Connected to sandbox {sandbox_id} successfully")

        return sandbox

    async def delete_sandbox(self, sandbox_id: str):
        """Delete a sandbox.

        Args:
            sandbox_id: Internal sandbox ID

        """
        await self._ensure_consumer_started()
        try:
            # Get sandbox data to retrieve provider_sandbox_id
            sandbox_data = await Sandboxes.get_sandbox_by_id(sandbox_id)
            if not sandbox_data:
                raise Exception(f"Sandbox {sandbox_id} not found")
            
            await self.sandbox_provider.delete(
                provider_sandbox_id=str(sandbox_data.provider_sandbox_id),
                config=self.sandbox_config,
                queue=self.queue_provider,
                sandbox_id=sandbox_id,
            )
            await Sandboxes.delete_sandbox(sandbox_id)
        except Exception as e:
            logger.error(f"Failed to delete sandbox {sandbox_id}: {e}")
            raise Exception(f"Failed to delete sandbox {sandbox_id}: {e}") from e
    
    async def get_sandbox_info(self, sandbox_id: str) -> Optional[SandboxInfo]:
        """Get sandbox information.

        Args:
            sandbox_id: Internal sandbox ID

        Returns:
            Sandbox information or None if not found
        """
        sandbox = await Sandboxes.get_sandbox_by_id(sandbox_id)
        if not sandbox:
            raise Exception(f"Sandbox {sandbox_id} not found")

        return self._convert_to_sandbox_info(sandbox)
    
    async def pause_sandbox(self, sandbox_id: str, reason: str = "manual"):
        """Pause a sandbox by stopping it and updating its status.
        
        Args:
            sandbox_id: Internal sandbox ID
            reason: Reason for pausing (e.g., 'timeout', 'manual')
            
        Returns:
            SandboxBaseProvider if paused successfully
        """
        await self._ensure_consumer_started()
        try:
            # Get sandbox data to retrieve provider_sandbox_id
            sandbox_data = await Sandboxes.get_sandbox_by_id(sandbox_id)
            if not sandbox_data:
                raise Exception(f"Sandbox {sandbox_id} not found")
            
            # Stop the sandbox with provider (timeout cancellation is handled inside stop method)
            await self.sandbox_provider.stop(
                provider_sandbox_id=str(sandbox_data.provider_sandbox_id),
                config=self.sandbox_config,
                queue=self.queue_provider,
                sandbox_id=sandbox_id,
            )
            await Sandboxes.update_sandbox_status(
                    sandbox_id,
                    "paused",
                    stopped_at=True
                )
            logger.info(f"Paused sandbox {sandbox_id} due to {reason}")
        except Exception as e:
            raise Exception(f"Error pausing sandbox {sandbox_id}: {e}") from e
    
    async def _ensure_consumer_started(self) -> None:
        """Ensure the queue consumer is started."""
        async with self._consumer_lock:
            if self._consumer_task is None and self.queue_provider:
                self._consumer_task = asyncio.create_task(self._setup_queue_consumer())
    
    async def _setup_queue_consumer(self) -> None:
        """Setup the message queue consumer for handling lifecycle events."""
        if not self.queue_provider:
            return
            
        try:
            await self.queue_provider.setup_consumer(self._handle_lifecycle_message)
            await self.queue_provider.start_consuming()
            logger.info("Message queue consumer started successfully")
        except Exception as e:
            logger.error(f"Failed to setup queue consumer: {e}")
    
    async def _handle_lifecycle_message(
        self, 
        sandbox_id: str, 
        action: str, 
        metadata: Dict[str, Any]
    ) -> None:
        """Handle lifecycle messages from the queue.
        
        Args:
            sandbox_id: ID of the sandbox
            action: Action to perform ('pause', 'terminate', etc.)
            metadata: Additional message metadata
        """
        try:
            logger.info(f"Processing lifecycle action '{action}' for sandbox {sandbox_id}")
            
            if action == "pause":
                reason = metadata.get("reason", "scheduled")
                await self.pause_sandbox(sandbox_id, reason)
            
            elif action == "terminate":
                await self.delete_sandbox(sandbox_id)
            else:
                logger.warning(f"Unknown lifecycle action '{action}' for sandbox {sandbox_id}")
                
        except Exception as e:
            raise Exception(f"Error handling lifecycle message for sandbox {sandbox_id}: {e}") from e
    
    async def shutdown(self) -> None:
        """Shutdown the sandbox manager and cleanup resources."""
        try:
            if self._consumer_task:
                self._consumer_task.cancel()
                try:
                    await self._consumer_task
                except asyncio.CancelledError:
                    pass
            if self.queue_provider:
                await self.queue_provider.stop_consuming()
            logger.info("Message queue consumer stopped")
        except Exception as e:
            logger.error(f"Error stopping queue consumer: {e}")
    
    async def list_user_sandboxes(
        self, user_id: str, status: Optional[str] = None
    ) -> List[SandboxInfo]:
        """List sandboxes for a user.

        Args:
            user_id: User ID
            status: Optional status filter

        Returns:
            List of sandbox information
        """
        sandboxes = await Sandboxes.list_user_sandboxes(user_id, status)
        return [self._convert_to_sandbox_info(sandbox) for sandbox in sandboxes]

    async def write_file(
        self, sandbox_id: str, file_path: str, file_content: str | bytes | IO
    ):
        """Write content to a file in a sandbox.

        Args:
            sandbox_id: Internal sandbox ID
            file_path: File path in sandbox
            file_content: Content to write

        Returns:
            True if successful, False if sandbox not found
        """
        try: 
            sandbox = await self.connect_or_resume_sandbox(
                sandbox_id=sandbox_id,
            )
            await sandbox.write_file(
                file_content, file_path
            )
            await Sandboxes.update_last_activity(sandbox_id)
        except Exception as e:
            raise Exception(f"Failed to write file to sandbox {sandbox_id}: {e}") from e

    async def upload_file(
        self, sandbox_id: str, local_file_path: str, remote_file_path: str
    ):
        """Upload a file to a sandbox.

        Args:
            sandbox_id: Internal sandbox ID
            local_file_path: Path to the file on the local machine
            remote_file_path: Path to the file in the sandbox

        Returns:
            True if successful, False if sandbox not found
        """
        try:
            sandbox = await self.connect_or_resume_sandbox(
                sandbox_id=sandbox_id,
            )
            await sandbox.upload_file(
                local_file_path, remote_file_path
            )
            await Sandboxes.update_last_activity(sandbox_id)
        except Exception as e:
            raise Exception(f"Failed to upload file to sandbox {sandbox_id}: {e}") from e


    async def download_file(
        self, sandbox_id: str, remote_file_path: str, format: Literal["text", "bytes", "stream"] = "text"
    ) -> Optional[str | bytearray | AsyncIterator[bytes]]:
        """Download a file from a sandbox.

        Args:
            sandbox_id: Internal sandbox ID
            remote_file_path: Path to the file in the sandbox
            local_file_path: Path to the file on the local machine

        Returns:
            File content as string, bytes, or iterator of bytes, None if sandbox not found
        """

        # Update last activity
        try:
            sandbox = await self.connect_or_resume_sandbox(
                sandbox_id=sandbox_id,
            )
            await Sandboxes.update_last_activity(sandbox_id)
            return await sandbox.download_file(
                remote_file_path, format
            )
        except Exception as e:
            raise Exception(f"Failed to download file from sandbox {sandbox_id}: {e}") from e

    async def read_file(
        self, sandbox_id: str, file_path: str
    ) -> Optional[str]:
        """Read a file from a sandbox.

        Args:
            sandbox_id: Internal sandbox ID
            file_path: File path in sandbox

        Returns:
            File content as string, None if sandbox not found
        """
        try:
            sandbox = await self.connect_or_resume_sandbox(
                sandbox_id=sandbox_id,
            )
            await Sandboxes.update_last_activity(sandbox_id)
            return await sandbox.read_file(
                file_path
            )
        except Exception as e:
            raise Exception(f"Failed to read file from sandbox {sandbox_id}: {e}") from e

    def _convert_to_sandbox_info(self, sandbox: Sandbox) -> SandboxInfo:
        """Convert database model to API response model.

        Args:
            sandbox: Database sandbox model

        Returns:
            Sandbox information for API response
        """
        DEFAULT_CPU_LIMIT = 1000
        DEFAULT_MEMORY_LIMIT = 512
        DEFAULT_DISK_LIMIT = 1024
        DEFAULT_NETWORK_ENABLED = True


        return SandboxInfo(
            id=str(sandbox.id),
            provider=str(sandbox.provider),
            user_id=str(sandbox.user_id),
            provider_sandbox_id=str(sandbox.provider_sandbox_id),
            template="base",  # Default template value
            status=str(sandbox.status),
            cpu_limit=DEFAULT_CPU_LIMIT,  # Default value since cpu_limit column removed
            memory_limit=DEFAULT_MEMORY_LIMIT,  # Default value since memory_limit column removed
            disk_limit=DEFAULT_DISK_LIMIT,  # Default value since disk_limit column removed
            network_enabled=DEFAULT_NETWORK_ENABLED,  # Default value since network_enabled column removed
            metadata=None,  # Default value since sandbox_metadata column removed
            created_at=sandbox.created_at.isoformat() if sandbox.created_at else "",
            started_at=sandbox.started_at.isoformat() if sandbox.started_at else None,
            stopped_at=sandbox.stopped_at.isoformat() if sandbox.stopped_at else None,
            last_activity_at=sandbox.last_activity_at.isoformat()
            if sandbox.last_activity_at
            else None,
        )
