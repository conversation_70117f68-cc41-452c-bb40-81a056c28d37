"""MCP settings service layer."""

from datetime import datetime, timezone
from typing import Optional
import uuid
from sqlalchemy import select
from ii_agent.db.models import MCPSetting
from ii_agent.server.mcp_settings.models import (
    MCPSettingCreate,
    MCPSettingUpdate,
    MCPSettingInfo,
    MCPSettingList,
)
from ii_agent.db.manager import DBSession


async def create_mcp_settings(
    *, db_session: DBSession, mcp_setting_in: MCPSettingCreate, user_id: str
) -> MCPSettingInfo:
    """Create new MCP settings for a user."""

    # Deactivate any existing active settings for this user
    existing_active_settings = (
        (
            await db_session.execute(
                select(MCPSetting).filter(
                    MCPSetting.user_id == user_id,
                    MCPSetting.is_active,
                )
            )
        )
        .scalars()
        .all()
    )

    for setting in existing_active_settings:
        setting.is_active = False
        setting.updated_at = datetime.now(timezone.utc)

    # Create new settings (always create, never update)
    new_setting = MCPSetting(
        id=str(uuid.uuid4()),
        user_id=user_id,
        mcp_config=mcp_setting_in.mcp_config,
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )

    db_session.add(new_setting)
    await db_session.commit()
    await db_session.refresh(new_setting)

    return _to_mcp_setting_info(new_setting)


async def update_mcp_settings(
    *,
    db_session: DBSession,
    setting_id: str,
    setting_update: MCPSettingUpdate,
    user_id: str,
) -> Optional[MCPSettingInfo]:
    """Update existing MCP settings."""

    setting = (
        await db_session.execute(
            select(MCPSetting).filter(
                MCPSetting.id == setting_id,
                MCPSetting.user_id == user_id,
            )
        )
    ).scalar_one_or_none()

    if not setting:
        return None

    # Update only provided fields
    if setting_update.mcp_config is not None:
        setting.mcp_config = setting_update.mcp_config
    if setting_update.is_active is not None:
        setting.is_active = setting_update.is_active

    setting.updated_at = datetime.now(timezone.utc)

    await db_session.commit()
    await db_session.refresh(setting)

    return _to_mcp_setting_info(setting)


async def get_mcp_settings(
    *, db_session: DBSession, setting_id: str, user_id: str
) -> Optional[MCPSettingInfo]:
    """Get MCP settings by ID."""

    setting = (
        await db_session.execute(
            select(MCPSetting).filter(
                MCPSetting.id == setting_id,
                MCPSetting.user_id == user_id,
            )
        )
    ).scalar_one_or_none()

    if not setting:
        return None

    return _to_mcp_setting_info(setting)


async def list_mcp_settings(
    *, db_session: DBSession, user_id: str, only_active: bool = False
) -> MCPSettingList:
    """List all MCP settings for a user."""

    query = select(MCPSetting).filter(MCPSetting.user_id == user_id)

    if only_active:
        query = query.filter(MCPSetting.is_active)

    query = query.order_by(MCPSetting.created_at.desc())

    settings = (await db_session.execute(query)).scalars().all()

    settings_list = [_to_mcp_setting_info(setting) for setting in settings]

    return MCPSettingList(settings=settings_list)


async def delete_mcp_settings(
    *, db_session: DBSession, setting_id: str, user_id: str
) -> bool:
    """Delete MCP settings by ID."""

    setting = (
        await db_session.execute(
            select(MCPSetting).filter(
                MCPSetting.id == setting_id,
                MCPSetting.user_id == user_id,
            )
        )
    ).scalar_one_or_none()

    if not setting:
        return False

    await db_session.delete(setting)
    await db_session.commit()

    return True


# Helper functions
def _to_mcp_setting_info(setting: MCPSetting) -> MCPSettingInfo:
    """Convert database model to Pydantic model."""
    return MCPSettingInfo(
        id=setting.id,
        mcp_config=setting.mcp_config or {},
        is_active=setting.is_active,
        created_at=setting.created_at.isoformat() if setting.created_at else "",
        updated_at=setting.updated_at.isoformat() if setting.updated_at else None,
    )
